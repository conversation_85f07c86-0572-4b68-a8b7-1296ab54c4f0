package bot

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"
)

// CopyProjectReq 复制项目请求参数
type CopyProjectReq struct {
	SourceProjectID     string           `json:"source_project_id"`     // 源项目ID
	SourceAdvertiserID  int64            `json:"source_advertiser_id"`  // 源项目广告主ID（用于获取项目详情）
	AdvertiserID        int64            `json:"advertiser_id"`         // 目标广告主ID（新项目所属的广告主）
	NewProjectName      string           `json:"new_project_name"`      // 新项目名称（可选，不传则自动生成）
	CopyCount           int              `json:"copy_count"`            // 复制数量（默认为1）
	Cookie              string           `json:"cookie"`                // Cookie
	SourceProjectDetail *ProjectInfoData `json:"source_project_detail"` // 源项目详情数据（可选，前端传递可避免重复获取）
}

// CopyProjectResp 复制项目响应
type CopyProjectResp struct {
	Success         bool                 `json:"success"`          // 是否成功
	Message         string               `json:"message"`          // 响应消息
	SourceProject   *ProjectInfoData     `json:"source_project"`   // 源项目信息
	CreatedProject  *CreateProjectResp   `json:"created_project"`  // 创建的新项目信息（单个复制时使用）
	CreatedProjects []*CreateProjectResp `json:"created_projects"` // 创建的新项目信息列表（批量复制时使用）
	ErrorCode       string               `json:"error_code"`       // 错误码
}

// CopyProject 复制项目
func (b *Bot) CopyProject(req *CopyProjectReq) (*CopyProjectResp, error) {
	result := &CopyProjectResp{
		Success: false,
	}

	// 验证复制数量
	if req.CopyCount <= 0 {
		req.CopyCount = 1
	}

	//获取源项目详情
	fmt.Printf("开始复制项目，源项目ID: %s，目标广告主ID: %d\n", req.SourceProjectID, req.AdvertiserID)

	var sourceProjectInfo *ProjectInfoData
	var err error

	// 优先使用前端传递的项目详情数据，避免重复获取
	if req.SourceProjectDetail != nil {
		sourceProjectInfo = req.SourceProjectDetail
		fmt.Printf("使用前端传递的项目详情数据，项目名称: %s\n", sourceProjectInfo.Name)
	} else {
		// 如果前端没有传递 则调用API获取
		sourceProjectInfo, err = b.getSourceProjectInfo(req.SourceAdvertiserID, req.SourceProjectID, req.Cookie)
		if err != nil {
			result.Message = fmt.Sprintf("获取源项目详情失败: %v", err)
			result.ErrorCode = "GET_SOURCE_PROJECT_FAILED"
			return result, err
		}
		fmt.Printf("成功获取源项目详情，项目名称: %s\n", sourceProjectInfo.Name)
	}

	result.SourceProject = sourceProjectInfo
	j, _ := json.MarshalIndent(sourceProjectInfo, "", "  ")
	fmt.Printf("前端返回的项目详情:\n%s\n", string(j))
	// 转换项目信息
	createReqInterface, err := b.convertProjectInfoToCreateRequest(sourceProjectInfo)
	if err != nil {
		result.Message = fmt.Sprintf("转换项目信息失败: %v", err)
		result.ErrorCode = "CONVERT_PROJECT_INFO_FAILED"
		return result, err
	}

	// 根据项目类型进行类型断言
	var createResp *CreateProjectResp
	if leadReq, ok := createReqInterface.(*CreateProjectLeadGenerationReq); ok {
		//销售线索
		createResp, err = b.CreateProjectLeadGeneration(req.AdvertiserID, leadReq, req.Cookie)
	} else if normalReq, ok := createReqInterface.(*CreateProjectAppPromotionReq); ok {
		//应用推广
		createResp, err = b.CreateProjectAppPromotionation(req.AdvertiserID, normalReq, req.Cookie)
	} else if nativeReq, ok := createReqInterface.(*CreateProjectNativeInteractionReq); ok {
		//原生互动
		createResp, err = b.CreateProjectNativeInteraction(req.AdvertiserID, nativeReq, req.Cookie)
	} else if ecommerceReq, ok := createReqInterface.(*CreateProjectEcommerceReq); ok {
		//商品目录
		createResp, err = b.CreateProjectEcommerce(req.AdvertiserID, ecommerceReq, req.Cookie)
	} else {
		result.Message = "不支持的项目类型"
		result.ErrorCode = "UNSUPPORTED_PROJECT_TYPE"
		return result, fmt.Errorf("不支持的项目类型")
	}

	if err != nil {
		result.Message = fmt.Sprintf("创建项目失败: %v", err)
		result.ErrorCode = "CREATE_PROJECT_FAILED"
		return result, err
	}

	if createResp.Code != 0 {
		result.Message = fmt.Sprintf("创建项目接口返回错误: %s", createResp.Msg)
		result.ErrorCode = "CREATE_PROJECT_API_ERROR"
		result.CreatedProject = createResp
		return result, fmt.Errorf("创建项目失败: %s", createResp.Msg)
	}

	result.Success = true
	result.Message = createResp.Msg
	result.CreatedProject = createResp

	fmt.Printf("项目复制成功！新项目ID: %s\n",
		getProjectIDFromCreateResp(createResp))

	return result, nil
}

// convertProjectInfoToCreateRequest 根据不同的项目类型 将项目详情参数转换为创建项目请求参数
func (b *Bot) convertProjectInfoToCreateRequest(sourceProject *ProjectInfoData) (interface{}, error) {
	fmt.Printf("开始转换项目信息为创建请求\n")

	data := &sourceProject.Data

	newProjectName := generateNewProjectName(sourceProject.Name)

	// 根据LandingType区分处理不同类型的项目
	landingType := data.MarketingInfo.LandingType
	fmt.Printf("项目落地页类型: %d\n", landingType)

	// 根据落地页类型使用不同的数据结构
	if data.IsAppPromotion() {
		//推广应用
		return b.convertAppPromotionToCreateRequest(sourceProject, newProjectName)
	} else if data.IsLeadGeneration() {
		//销售线索
		return b.convertLeadGenerationToCreateRequest(sourceProject, newProjectName)
	} else if data.IsNativeInteraction() {
		//原生互动
		return b.convertNativeInteractionToCreateRequest(sourceProject, newProjectName)
	} else if data.IsEcommerce() {
		//商品目录
		return b.convertEcommerceToCreateRequest(sourceProject, newProjectName)
	} else {
		return nil, fmt.Errorf("不支持的项目类型")
	}
}

// convertAppPromotionToCreateRequest 转换应用推广项目为创建请求
func (b *Bot) convertAppPromotionToCreateRequest(sourceProject *ProjectInfoData, newProjectName string) (*CreateProjectAppPromotionReq, error) {
	fmt.Printf("转换应用推广项目数据\n")
	data := &sourceProject.Data
	bid, err := data.PromotionStrategy.Bid.Float64()
	if err != nil {
		return nil, err
	}
	budget, err := data.PromotionStrategy.Budget.Float64()
	if err != nil {
		return nil, err
	}
	// 构建应用推广项目请求
	req := &CreateProjectAppPromotionReq{
		// 基础字段
		Ac:                     sourceProject.AC,
		ActionInterestSelect:   "1", // 应用推广项目默认开启行为兴趣选择
		ActionTrackUrl:         []string{},
		Age:                    data.Audience.Age,
		AppName:                data.PromotionObject.AppName,
		AppPromotionType:       data.MarketingInfo.AppPromotionType,
		AppType:                data.PromotionObject.AppType,
		AssetIds:               data.PromotionStrategy.AssetIDs,
		AudienceExtend:         data.PromotionStrategy.AudienceExtend,
		AutoExtendEnabled:      data.Audience.AutoExtendEnabled,
		AutoExtendTargets:      data.Audience.AutoExtendTargets,
		AwemeFansSelect:        "0",
		AutoAdType:             data.MarketingInfo.AutoAdType,
		Bid:                    bid,
		Budget:                 budget,
		BudgetMode:             data.PromotionStrategy.BudgetMode,
		CampaignType:           data.MarketingInfo.CampaignType,
		CdpMarketingGoal:       data.MarketingInfo.CdpMarketingGoal,
		ConvertSource:          data.PromotionStrategy.ConvertSource,
		ConvertedTimeDuration:  data.Audience.ConvertedTimeDuration, // 应用推广项目默认转化时间周期
		DeliveryMode:           data.MarketingInfo.DeliveryMode,
		DeliveryPackage:        data.MarketingInfo.DeliveryPackage,
		DeliveryProduct:        data.PromotionObject.DeliveryProduct,
		DeliveryRelatedNum:     data.MarketingInfo.DeliveryRelatedNum,
		DeviceType:             sourceProject.DeviceType,
		District:               data.Audience.District,
		DownloadType:           data.PromotionObject.DownloadType,
		DownloadUrl:            data.PromotionObject.DownloadURL,
		EffectiveFrame:         []string{},
		ExternalAction:         data.PromotionStrategy.ExternalAction,
		FeedDeliverySearch:     data.PromotionStrategy.FeedDeliverySearch,
		FirstFrame:             []string{},
		FlowControlMode:        data.PromotionStrategy.FlowControlMode,
		Gender:                 data.Audience.Gender,
		HideIfConverted:        data.Audience.HideIfConverted,
		HideIfExists:           data.Audience.HideIfExists,
		InventoryCatalog:       data.Inventory.InventoryCatalog,
		InventoryType:          data.Inventory.InventoryType,
		IosOsvSelect:           "0",
		IsSearch3Online:        true,
		IsSearchSpeedPhaseFour: false,
		Keywords:               []interface{}{},
		LandingType:            data.MarketingInfo.LandingType, // 3 - 应用推广
		LastFrame:              []string{},
		LaunchPrice:            []interface{}{},
		LaunchPriceSelect:      "0",
		Name:                   newProjectName,
		OriginProjectId:        sourceProject.ID,
		PackageName:            data.PromotionObject.PackageName,
		Platform:               sourceProject.Platform,
		PricingType:            data.PromotionStrategy.PricingType,
		Products:               []interface{}{},
		RetargetingTags:        data.Audience.RetargetingTags,
		RetargetingTagsExclude: data.Audience.RetargetingTagsExclude,
		ScheduleType:           data.PromotionStrategy.ScheduleType,
		SearchBidRatio:         data.PromotionStrategy.SearchBidRatio.String(),
		SmartBidType:           data.PromotionStrategy.SmartBidType,
		SmartInventory:         data.Inventory.SmartInventory,
		SmartInterestAction:    data.Audience.SmartInterestAction,
		SuperiorPopularityType: "0",
		TrackUrl:               []string{},
		TrackUrlGroupId:        data.PromotionStrategy.TrackURLGroupID,
		TrackUrlGroupInfo: struct {
			ActionTrackUrl    string `json:"action_track_url"`
			ActiveTrackUrl    string `json:"active_track_url"`
			DisplayTrackUrl   string `json:"display_track_url"`
			DownloadUrl       string `json:"download_url"`
			EffectiveFrame    string `json:"effective_frame"`
			FirstFrame        string `json:"first_frame"`
			LastFrame         string `json:"last_frame"`
			TrackUrlGroupId   string `json:"track_url_group_id"`
			TrackUrlGroupName string `json:"track_url_group_name"`
		}{
			ActionTrackUrl:    data.TrackUrls.ActionTrackURL[0],
			ActiveTrackUrl:    "",
			DisplayTrackUrl:   "",
			DownloadUrl:       data.PromotionObject.DownloadURL,
			EffectiveFrame:    data.TrackUrls.EffectiveFrame[0],
			FirstFrame:        data.TrackUrls.FirstFrame[0],
			LastFrame:         data.TrackUrls.LastFrame[0],
			TrackUrlGroupId:   data.PromotionStrategy.TrackURLGroupID,
			TrackUrlGroupName: newProjectName,
		},
		TrackUrlSendType: data.TrackUrls.TrackURLSendType,
		TrackUrlType:     data.PromotionStrategy.TrackURLType,
		WeekScheduleType: 0,
	}

	// 设置重定向标签排除（如果存在）
	if len(data.Audience.RetargetingTagsExclude) > 0 {
		req.RetargetingTagsExclude = data.Audience.RetargetingTagsExclude
	}

	// 设置监测链接（如果存在）
	if len(data.TrackUrls.TrackURL) > 0 {
		req.TrackUrl = make([]string, len(data.TrackUrls.TrackURL))
		for i, url := range data.TrackUrls.TrackURL {
			req.TrackUrl[i] = url
		}
	}

	// 设置点击链路（如果存在）
	if len(data.TrackUrls.ActionTrackURL) > 0 {
		req.ActionTrackUrl = make([]string, len(data.TrackUrls.ActionTrackURL))
		for i, url := range data.TrackUrls.ActionTrackURL {
			req.ActionTrackUrl[i] = url
		}
	}

	// 设置视频播放链路（如果存在）
	if len(data.TrackUrls.FirstFrame) > 0 {
		req.FirstFrame = make([]string, len(data.TrackUrls.FirstFrame))
		for i, url := range data.TrackUrls.FirstFrame {
			req.FirstFrame[i] = url
		}
	}

	// 设置视频完播链路（如果存在）
	if len(data.TrackUrls.LastFrame) > 0 {
		req.LastFrame = make([]string, len(data.TrackUrls.LastFrame))
		for i, url := range data.TrackUrls.LastFrame {
			req.LastFrame[i] = url
		}
	}

	// 设置视频有效播放链路（如果存在）
	if len(data.TrackUrls.EffectiveFrame) > 0 {
		req.EffectiveFrame = make([]string, len(data.TrackUrls.EffectiveFrame))
		for i, url := range data.TrackUrls.EffectiveFrame {
			req.EffectiveFrame[i] = url
		}
	}

	// 设置产品列表（如果存在）
	if data.AutoExtend != nil && len(data.AutoExtend.Products) > 0 {
		req.Products = make([]interface{}, len(data.AutoExtend.Products))
		for i, product := range data.AutoExtend.Products {
			req.Products[i] = product
		}
	}

	fmt.Printf("应用推广项目转换完成，项目名称: %s\n", newProjectName)
	return req, nil
}

// convertLeadGenerationToCreateRequest 转换销售线索推广项目为创建请求
func (b *Bot) convertLeadGenerationToCreateRequest(sourceProject *ProjectInfoData, newProjectName string) (*CreateProjectLeadGenerationReq, error) {
	fmt.Printf("转换销售线索推广项目数据\n")
	data := &sourceProject.Data
	// 构建销售线索项目请求
	req := &CreateProjectLeadGenerationReq{
		// 基础字段
		Ac:                   sourceProject.AC,
		ActionInterestSelect: "0",
		ActionTrackUrl:       []interface{}{},
		Age:                  data.Audience.Age,
		AssetType:            data.PromotionObject.AssetType,
		AudienceSupportKeys: []string{
			"District", "City", "Geolocation", "CityDivide", "LocationType", "RegionVer",
			"Age", "Gender", "RetargetingTags", "RetargetingTagsExclude",
			"HideIfConverted", "ConvertedTimeDuration", "FilterAwemeAbnormalActive",
			"FilterAwemeFansCount", "AudiencePackageId", "AutoExtendEnabled", "AutoExtendTargets",
			"FilterOwnAwemeFans", "Ac", "LaunchPrice", "SmartInterestAction", "InterestCategories",
			"InterestWords", "ActionCategories", "ActionDays", "ActionScene", "ActionWords",
		},
		AutoAdType:                data.MarketingInfo.AutoAdType,
		Bid:                       b.convertBidToFloat(data.PromotionStrategy.Bid.String()),
		Budget:                    b.convertBudgetToInt(data.PromotionStrategy.Budget.String()),
		BudgetMode:                data.PromotionStrategy.BudgetMode,
		CampaignType:              data.MarketingInfo.CampaignType,
		CdpMarketingGoal:          data.MarketingInfo.CdpMarketingGoal,
		ClueAcquisitionMethod:     data.PromotionObject.ClueAcquisitionMethod,
		ConvertedTimeDuration:     data.Audience.ConvertedTimeDuration, // 销售线索项目默认转化时间周期
		DeliveryMode:              data.MarketingInfo.DeliveryMode,
		DeliveryPackage:           data.MarketingInfo.DeliveryPackage,
		DeliveryProduct:           data.PromotionObject.DeliveryProduct,
		DeliveryRelatedNum:        data.MarketingInfo.DeliveryRelatedNum,
		DeliveryScene:             data.AutoExtend.DeliveryScene, // 销售线索项目默认投放场景
		District:                  data.Audience.District,        // 销售线索项目默认全国投放
		EffectiveFrame:            []interface{}{},
		ExternalAction:            data.PromotionStrategy.ExternalAction,
		FilterAwemeAbnormalActive: data.Audience.FilterAwemeAbnormalActive,
		FilterAwemeFansCount:      data.Audience.FilterAwemeFansCount,
		FilterOwnAwemeFans:        data.Audience.FilterOwnAwemeFans,
		FirstFrame:                []interface{}{},
		FlowControlMode:           data.PromotionStrategy.FlowControlMode,
		Gender:                    data.Audience.Gender,
		HideIfConverted:           data.Audience.HideIfConverted,
		InventoryCatalog:          data.Inventory.InventoryCatalog,
		IsSearch3Online:           true,
		IsSearchSpeedPhaseFour:    false,
		LandingType:               data.MarketingInfo.LandingType, // 1 - 销售线索
		LastFrame:                 []interface{}{},
		LaunchPrice:               []interface{}{},
		LaunchPriceSelect:         "0",
		MultiAssetSwitch:          0, // 销售线索项目默认关闭多素材开关
		Name:                      newProjectName,
		OriginProjectId:           sourceProject.ID,
		PricingType:               data.PromotionStrategy.PricingType,
		RetargetingTags:           data.Audience.RetargetingTags,
		RetargetingTagsExclude:    data.Audience.RetargetingTagsExclude,
		ScheduleType:              data.PromotionStrategy.ScheduleType,
		SmartBidType:              data.PromotionStrategy.SmartBidType,
		TrackUrl:                  []interface{}{},
		TrackUrlGroupInfo:         struct{}{},
		TrackUrlSendType:          data.TrackUrls.TrackURLSendType,
		WeekScheduleType:          0,
	}
	if len(data.AutoExtend.Products) > 0 {
		req.Products = make([]struct {
			ProductId         string `json:"ProductId"`
			ProductPlatformId string `json:"ProductPlatformId"`
			UniqueProductId   string `json:"UniqueProductId"`
		}, len(data.AutoExtend.Products))
		for i, product := range data.AutoExtend.Products {
			req.Products[i] = struct {
				ProductId         string `json:"ProductId"`
				ProductPlatformId string `json:"ProductPlatformId"`
				UniqueProductId   string `json:"UniqueProductId"`
			}{
				ProductId:         product.ProductID,
				ProductPlatformId: product.ProductPlatformID,
				UniqueProductId:   product.UniqueProductID,
			}
		}
	}
	// 设置重定向标签排除（如果存在）
	if len(data.Audience.RetargetingTagsExclude) > 0 {
		req.RetargetingTagsExclude = data.Audience.RetargetingTagsExclude
	}

	// 设置监测链接（如果存在）
	if len(data.TrackUrls.TrackURL) > 0 {
		req.TrackUrl = make([]interface{}, len(data.TrackUrls.TrackURL))
		for i, url := range data.TrackUrls.TrackURL {
			req.TrackUrl[i] = url
		}
	}

	// 设置点击链路（如果存在）
	if len(data.TrackUrls.ActionTrackURL) > 0 {
		req.ActionTrackUrl = make([]interface{}, len(data.TrackUrls.ActionTrackURL))
		for i, url := range data.TrackUrls.ActionTrackURL {
			req.ActionTrackUrl[i] = url
		}
	}

	// 设置视频播放链路（如果存在）
	if len(data.TrackUrls.FirstFrame) > 0 {
		req.FirstFrame = make([]interface{}, len(data.TrackUrls.FirstFrame))
		for i, url := range data.TrackUrls.FirstFrame {
			req.FirstFrame[i] = url
		}
	}

	// 设置视频完播链路（如果存在）
	if len(data.TrackUrls.LastFrame) > 0 {
		req.LastFrame = make([]interface{}, len(data.TrackUrls.LastFrame))
		for i, url := range data.TrackUrls.LastFrame {
			req.LastFrame[i] = url
		}
	}

	// 设置视频有效播放链路（如果存在）
	if len(data.TrackUrls.EffectiveFrame) > 0 {
		req.EffectiveFrame = make([]interface{}, len(data.TrackUrls.EffectiveFrame))
		for i, url := range data.TrackUrls.EffectiveFrame {
			req.EffectiveFrame[i] = url
		}
	}

	fmt.Printf("销售线索项目转换完成，项目名称: %s\n", newProjectName)
	return req, nil
}

// convertNativeInteractionToCreateRequest 转换原生互动项目为创建请求
func (b *Bot) convertNativeInteractionToCreateRequest(sourceProject *ProjectInfoData, newProjectName string) (*CreateProjectNativeInteractionReq, error) {
	fmt.Printf("转换原生互动项目数据\n")
	data := &sourceProject.Data
	// 构建销售线索项目请求
	req := &CreateProjectNativeInteractionReq{
		// 基础字段
		Ac:                   sourceProject.AC,
		ActionInterestSelect: "0",
		ActionTrackUrl:       []interface{}{},
		Age:                  data.Audience.Age,
		AssetType:            data.PromotionObject.AssetType,
		AudienceSupportKeys: []string{
			"District", "City", "Geolocation", "CityDivide", "LocationType", "RegionVer",
			"Age", "Gender", "RetargetingTags", "RetargetingTagsExclude",
			"HideIfConverted", "ConvertedTimeDuration", "FilterAwemeAbnormalActive",
			"FilterAwemeFansCount", "AudiencePackageId", "AutoExtendEnabled", "AutoExtendTargets",
			"FilterOwnAwemeFans", "Ac", "LaunchPrice", "SmartInterestAction", "InterestCategories",
			"InterestWords", "ActionCategories", "ActionDays", "ActionScene", "ActionWords",
		},
		AutoAdType:                data.MarketingInfo.AutoAdType,
		Bid:                       b.convertBidToFloat(data.PromotionStrategy.Bid.String()),
		Budget:                    b.convertBudgetToInt(data.PromotionStrategy.Budget.String()),
		BudgetMode:                data.PromotionStrategy.BudgetMode,
		CampaignType:              data.MarketingInfo.CampaignType,
		CdpMarketingGoal:          data.MarketingInfo.CdpMarketingGoal,
		ClueAcquisitionMethod:     data.PromotionObject.ClueAcquisitionMethod,
		ConvertedTimeDuration:     data.Audience.ConvertedTimeDuration, // 销售线索项目默认转化时间周期
		DeliveryMode:              data.MarketingInfo.DeliveryMode,
		DeliveryPackage:           data.MarketingInfo.DeliveryPackage,
		DeliveryProduct:           data.PromotionObject.DeliveryProduct,
		DeliveryRelatedNum:        data.MarketingInfo.DeliveryRelatedNum,
		DeliveryScene:             data.AutoExtend.DeliveryScene, // 销售线索项目默认投放场景
		District:                  data.Audience.District,        // 销售线索项目默认全国投放
		EffectiveFrame:            []interface{}{},
		ExternalAction:            data.PromotionStrategy.ExternalAction,
		FilterAwemeAbnormalActive: data.Audience.FilterAwemeAbnormalActive,
		FilterAwemeFansCount:      data.Audience.FilterAwemeFansCount,
		FilterOwnAwemeFans:        data.Audience.FilterOwnAwemeFans,
		FirstFrame:                []interface{}{},
		FlowControlMode:           data.PromotionStrategy.FlowControlMode,
		Gender:                    data.Audience.Gender,
		HideIfConverted:           data.Audience.HideIfConverted,
		InventoryCatalog:          data.Inventory.InventoryCatalog,
		IsSearch3Online:           true,
		IsSearchSpeedPhaseFour:    false,
		LandingType:               data.MarketingInfo.LandingType, // 1 - 销售线索
		LastFrame:                 []interface{}{},
		LaunchPrice:               []interface{}{},
		LaunchPriceSelect:         "0",
		MultiAssetSwitch:          0, // 销售线索项目默认关闭多素材开关
		Name:                      newProjectName,
		OriginProjectId:           sourceProject.ID,
		PricingType:               data.PromotionStrategy.PricingType,
		RetargetingTags:           data.Audience.RetargetingTags,
		RetargetingTagsExclude:    data.Audience.RetargetingTagsExclude,
		ScheduleType:              data.PromotionStrategy.ScheduleType,
		SmartBidType:              data.PromotionStrategy.SmartBidType,
		TrackUrl:                  []interface{}{},
		TrackUrlGroupInfo:         struct{}{},
		TrackUrlSendType:          data.TrackUrls.TrackURLSendType,
		WeekScheduleType:          0,
	}
	if len(data.AutoExtend.Products) > 0 {
		req.Products = make([]struct {
			ProductId         string `json:"ProductId"`
			ProductPlatformId string `json:"ProductPlatformId"`
			UniqueProductId   string `json:"UniqueProductId"`
		}, len(data.AutoExtend.Products))
		for i, product := range data.AutoExtend.Products {
			req.Products[i] = struct {
				ProductId         string `json:"ProductId"`
				ProductPlatformId string `json:"ProductPlatformId"`
				UniqueProductId   string `json:"UniqueProductId"`
			}{
				ProductId:         product.ProductID,
				ProductPlatformId: product.ProductPlatformID,
				UniqueProductId:   product.UniqueProductID,
			}
		}
	}
	// 设置重定向标签排除（如果存在）
	if len(data.Audience.RetargetingTagsExclude) > 0 {
		req.RetargetingTagsExclude = data.Audience.RetargetingTagsExclude
	}

	// 设置监测链接（如果存在）
	if len(data.TrackUrls.TrackURL) > 0 {
		req.TrackUrl = make([]interface{}, len(data.TrackUrls.TrackURL))
		for i, url := range data.TrackUrls.TrackURL {
			req.TrackUrl[i] = url
		}
	}

	// 设置点击链路（如果存在）
	if len(data.TrackUrls.ActionTrackURL) > 0 {
		req.ActionTrackUrl = make([]interface{}, len(data.TrackUrls.ActionTrackURL))
		for i, url := range data.TrackUrls.ActionTrackURL {
			req.ActionTrackUrl[i] = url
		}
	}

	// 设置视频播放链路（如果存在）
	if len(data.TrackUrls.FirstFrame) > 0 {
		req.FirstFrame = make([]interface{}, len(data.TrackUrls.FirstFrame))
		for i, url := range data.TrackUrls.FirstFrame {
			req.FirstFrame[i] = url
		}
	}

	// 设置视频完播链路（如果存在）
	if len(data.TrackUrls.LastFrame) > 0 {
		req.LastFrame = make([]interface{}, len(data.TrackUrls.LastFrame))
		for i, url := range data.TrackUrls.LastFrame {
			req.LastFrame[i] = url
		}
	}

	// 设置视频有效播放链路（如果存在）
	if len(data.TrackUrls.EffectiveFrame) > 0 {
		req.EffectiveFrame = make([]interface{}, len(data.TrackUrls.EffectiveFrame))
		for i, url := range data.TrackUrls.EffectiveFrame {
			req.EffectiveFrame[i] = url
		}
	}

	fmt.Printf("销售线索项目转换完成，项目名称: %s\n", newProjectName)
	return req, nil
}

// convertEcommerceToCreateRequest 转换商品目录项目为创建请求
func (b *Bot) convertEcommerceToCreateRequest(sourceProject *ProjectInfoData, newProjectName string) (*CreateProjectEcommerceReq, error) {
	fmt.Printf("转换商品目录项目数据\n")
	data := &sourceProject.Data
	// 构建商品目录项目请求
	req := &CreateProjectEcommerceReq{
		// 基础字段
		Ac:                   sourceProject.AC,
		ActionInterestSelect: "0",
		ActionTrackUrl:       []string{},
		Age:                  data.Audience.Age,
		AssetType:            data.PromotionObject.AssetType,
		AudienceSupportKeys: []string{
			"District", "City", "Geolocation", "CityDivide", "LocationType", "RegionVer",
			"Age", "Gender", "RetargetingTags", "RetargetingTagsExclude",
			"FilterAwemeAbnormalActive", "FilterAwemeFansCount", "FilterOwnAwemeFans",
			"ConvertedTimeDuration", "SmartInterestAction", "InterestCategories",
			"InterestWords", "ActionCategories", "ActionDays", "ActionScene", "ActionWords",
		},
		AutoAdType:             data.MarketingInfo.AutoAdType,
		Bid:                    b.convertBidToFloat(data.PromotionStrategy.Bid.String()),
		Budget:                 b.convertBudgetToInt(data.PromotionStrategy.Budget.String()),
		BudgetMode:             data.PromotionStrategy.BudgetMode,
		CampaignType:           data.MarketingInfo.CampaignType,
		CdpMarketingGoal:       data.MarketingInfo.CdpMarketingGoal,
		ClueAcquisitionMethod:  data.PromotionObject.ClueAcquisitionMethod,
		ConvertedTimeDuration:  data.Audience.ConvertedTimeDuration, // 商品目录项目默认转化时间周期
		DeliveryMode:           data.MarketingInfo.DeliveryMode,
		DeliveryPackage:        data.MarketingInfo.DeliveryPackage,
		DeliveryProduct:        data.PromotionObject.DeliveryProduct,
		DeliveryRelatedNum:     data.MarketingInfo.DeliveryRelatedNum,
		District:               data.Audience.District, // 商品目录项目默认全国投放
		EffectiveFrame:         []string{},
		ExternalAction:         data.PromotionStrategy.ExternalAction,
		FeedDeliverySearch:     data.PromotionStrategy.FeedDeliverySearch,
		FirstFrame:             []string{},
		FlowControlMode:        data.PromotionStrategy.FlowControlMode,
		Gender:                 data.Audience.Gender,
		HideIfConverted:        data.Audience.HideIfConverted,
		HideIfExists:           data.Audience.HideIfExists,
		InventoryCatalog:       data.Inventory.InventoryCatalog,
		InventoryType:          data.Inventory.InventoryType,
		IosOsvSelect:           "0",
		IsSearch3Online:        true,
		IsSearchSpeedPhaseFour: false,
		Keywords:               []interface{}{},
		LandingType:            data.MarketingInfo.LandingType, // 10 - 商品目录
		LastFrame:              []string{},
		LaunchPrice:            []interface{}{},
		LaunchPriceSelect:      "0",
		MultiAssetSwitch:       0, // 商品目录项目默认关闭多素材开关
		Name:                   newProjectName,
		OriginProjectId:        sourceProject.ID,
		PricingType:            data.PromotionStrategy.PricingType,
		RetargetingTags:        data.Audience.RetargetingTags,
		RetargetingTagsExclude: data.Audience.RetargetingTagsExclude,
		ScheduleType:           data.PromotionStrategy.ScheduleType,
		SearchBidRatio:         data.PromotionStrategy.SearchBidRatio.String(),
		SmartBidType:           data.PromotionStrategy.SmartBidType,
		SmartInterestAction:    data.Audience.SmartInterestAction,
		SuperiorPopularityType: "0",

		TrackUrlSendType: "0",
		TrackUrlType:     0,
		WeekScheduleType: 0,
	}

	// 设置商品目录项目专用字段

	fmt.Printf("商品目录项目转换完成，项目名称: %s\n", newProjectName)
	return req, nil
}

// convertBudgetToInt 将预算字符串转换为整数
func (b *Bot) convertBudgetToInt(budgetStr string) int {
	if budgetStr == "" {
		return 0
	}

	// 移除可能的小数点和后缀
	// 例如 "300.00" -> "300"
	cleanStr := budgetStr
	if idx := strings.Index(cleanStr, "."); idx != -1 {
		cleanStr = cleanStr[:idx]
	}

	// 转换为整数
	if budgetInt, err := strconv.Atoi(cleanStr); err == nil {
		return budgetInt
	}

	// 如果转换失败，尝试解析为浮点数再转换为整数
	if budgetFloat, err := strconv.ParseFloat(budgetStr, 64); err == nil {
		return int(budgetFloat)
	}

	// 如果都失败，返回0
	fmt.Printf("警告: 无法转换预算字段: %s，使用默认值0\n", budgetStr)
	return 0
}

// convertBidToFloat 将出价字符串转换为浮点数
func (b *Bot) convertBidToFloat(bidStr string) float64 {
	if bidStr == "" {
		return 0.0
	}

	// 尝试解析为浮点数
	if bidFloat, err := strconv.ParseFloat(bidStr, 64); err == nil {
		return bidFloat
	}

	// 如果解析失败，返回0.0
	fmt.Printf("警告: 无法将出价字符串转换为数字: %s，使用默认值0.0\n", bidStr)
	return 0.0
}

// generateNewProjectName 生成新项目名称
func generateNewProjectName(originalName string) string {
	timestamp := time.Now().Format("01_02_15:04:05")

	// 如果原名称已包含复制标识，先移除
	if strings.Contains(originalName, "_复制_") {
		parts := strings.Split(originalName, "_复制_")
		originalName = parts[0]
	}

	return fmt.Sprintf("%s_复制_%s", originalName, timestamp)
}

// getProjectIDFromCreateResp 从创建项目响应中获取项目ID
func getProjectIDFromCreateResp(resp *CreateProjectResp) string {
	if resp.Data != nil {
		return resp.Data.ProjectId
	}
	return "未知"
}

// getSourceProjectInfo 获取源项目详情
func (b *Bot) getSourceProjectInfo(advertiserID int64, projectID string, cookie string) (*ProjectInfoData, error) {
	fmt.Printf("正在获取项目详情，项目ID: %s\n", projectID)

	projectIDs := []string{projectID}
	resp, err := b.GetProjectInfo(advertiserID, projectIDs, cookie)
	if err != nil {
		return nil, fmt.Errorf("调用GetProjectInfo失败: %v", err)
	}

	if resp.Code != 0 {
		return nil, fmt.Errorf("GetProjectInfo接口返回错误: %s", resp.Msg)
	}

	if len(resp.Data) == 0 {
		return nil, fmt.Errorf("未找到项目ID为 %s 的项目", projectID)
	}

	if len(resp.Data) > 1 {
		fmt.Printf("警告: 找到多个项目，使用第一个项目\n")
	}

	return &resp.Data[0], nil
}
