package service

import (
	"context"
	"fmt"
	"time"

	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/utils"

	"github.com/jinzhu/copier"
	"github.com/wailsapp/wails/v3/pkg/application"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/bot"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/constant"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/model"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/result"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/svc"
	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/types"
)

// ProjectService 项目服务
type ProjectService struct {
	svcCtx *svc.ServiceContext
	ctx    context.Context
	app    *application.App
}

// NewProjectService 创建项目服务
func NewProjectService(svcCtx *svc.ServiceContext) *ProjectService {
	return &ProjectService{
		svcCtx: svcCtx,
	}
}

func (s *ProjectService) OnStartup(ctx context.Context, options application.ServiceOptions) error {
	s.ctx = ctx
	return nil
}
func (s *ProjectService) SetApp(app *application.App) {
	s.app = app
}

// GetProjectList 从数据库获取项目列表
func (s *ProjectService) GetProjectList(req types.ProjectListReq) *result.Result[types.ProjectListResp] {
	// 构建查询条件
	conditions := make(map[string]interface{})
	if req.AccountId > 0 {
		conditions["account_id"] = req.AccountId
	}
	if req.ProjectName != "" {
		conditions["project_name"] = req.ProjectName
	}
	if req.Status != "" {
		conditions["project_status"] = req.Status
	}
	if req.Remark != "" {
		conditions["remark"] = req.Remark
	}

	// 处理额外的过滤条件
	if req.ExtraFilters != nil {
		for key, value := range req.ExtraFilters {
			if value != "" {
				conditions[key] = value
			}
		}
	}

	// 调用model层获取数据
	projects, total, err := s.svcCtx.ProjectModel.List(int(req.Page), int(req.PageSize), conditions)
	if err != nil {
		return result.ToError[types.ProjectListResp](result.ErrorSelect.AddError(err))
	}

	// 为每个项目获取策略绑定信息
	for _, project := range projects {
		strategies, err := s.svcCtx.StrategyBindingModel.GetStrategiesByBinding("project", project.ProjectId)
		if err == nil {
			strategyIds := make([]int64, len(strategies))
			for i, strategy := range strategies {
				strategyIds[i] = strategy.ID
			}
			project.StrategyIds = strategyIds
		}
	}

	//添加操作日志
	go func() {
		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   "",
				AccountId:     req.AccountId,
				Module:        constant.OperationLog_Module_Project,
				OperationType: constant.OperationLog_OperationType_Select,
				Operation:     "查询项目列表",
				Description:   fmt.Sprintf("账号ID: %d，查询项目列表", req.AccountId),
				OperationTime: time.Now(),
			})
		}
	}()
	resp := types.ProjectListResp{
		Total:    total,
		List:     projects,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	return result.SuccessResult(resp)
}

// GetProjectListAsync 获取项目列表
func (s *ProjectService) GetProjectListAsync(req types.AdvertiserReq) *result.Result[bot.GetProjectResp] {
	// 获取bot和账户信息
	b, accountInfo, err := s.svcCtx.GetBot(req.AccountId)
	if err != nil {
		return result.ToError[bot.GetProjectResp](result.ErrorSelect.AddError(err))
	}
	resp, err := b.GetProjectList(req.Page, req.PageSize, 1, req.Keyword, req.ProjectId, accountInfo.Cookie)
	if err != nil {
		return result.ToError[bot.GetProjectResp](result.ErrorSelect.AddError(err))
	}
	//添加操作日志
	go func() {
		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   accountInfo.AccountName,
				AccountId:     accountInfo.AccountId,
				Module:        constant.OperationLog_Module_Project,
				OperationType: constant.OperationLog_OperationType_Select,
				Operation:     "查询项目列表",
				Description:   fmt.Sprintf("账号ID: %d，账户名: %s，查询项目列表", accountInfo.AccountId, accountInfo.AccountName),
				OperationTime: time.Now(),
			})
		}
	}()
	return result.SuccessResult(*resp)
}

// DeleteProjectAsync 异步删除项目
func (s *ProjectService) DeleteProjectAsync(req types.DeleteProjectAsyncReq) *result.Result[bot.DeleteProjectResp] {
	// 获取bot和账户信息
	b, accountInfo, err := s.svcCtx.GetBot(req.AccountId)
	if err != nil {
		return result.ToError[bot.DeleteProjectResp](result.ErrorSelect.AddError(err))
	}

	resp, err := b.DeleteProject(req.AdvertiserId, req.ProjectIds, accountInfo.Cookie)
	if err != nil {
		return result.ToError[bot.DeleteProjectResp](result.ErrorSelect.AddError(err))
	}
	//添加操作日志
	go func() {
		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   accountInfo.AccountName,
				AccountId:     accountInfo.AccountId,
				Module:        constant.OperationLog_Module_Project,
				OperationType: constant.OperationLog_OperationType_Delete,
				Operation:     "删除广告主下项目",
				Description:   fmt.Sprintf("账号ID: %d，账户名: %s，删除[%s]广告主下项目[%v]", accountInfo.AccountId, accountInfo.AccountName, req.AdvertiserId, req.ProjectIds),
				OperationTime: time.Now(),
			})
		}
	}()
	return result.SuccessResult(*resp)
}

// BatchDeleteProjectAsync 异步批量删除项目
func (s *ProjectService) BatchDeleteProjectAsync(req types.BatchDeleteProjectAsyncReq) *result.Result[bot.BatchDeleteProjectResp] {
	// 获取bot和账户信息
	b, accountInfo, err := s.svcCtx.GetBot(req.AccountId)
	if err != nil {
		return result.ToError[bot.BatchDeleteProjectResp](result.ErrorSelect.AddError(err))
	}

	resp, err := b.BatchDeleteProject(req.AccountDetailList, accountInfo.Cookie)
	if err != nil {
		return result.ToError[bot.BatchDeleteProjectResp](result.ErrorSelect.AddError(err))
	}
	//添加操作日志
	go func() {
		if s.app != nil {
			descStr := ""
			for _, item := range req.AccountDetailList {
				descStr += fmt.Sprintf("[%s]广告主下项目[%v],  ", item.AdvertiserId, item.Id)
			}
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   accountInfo.AccountName,
				AccountId:     accountInfo.AccountId,
				Module:        constant.OperationLog_Module_Project,
				OperationType: constant.OperationLog_OperationType_Delete,
				Operation:     "跨广告主删除项目",
				Description:   fmt.Sprintf("账号ID: %d，账户名: %s，跨广告主删除项目,删除 %s", accountInfo.AccountId, accountInfo.AccountName, descStr),
				OperationTime: time.Now(),
			})
		}
	}()
	return result.SuccessResult(*resp)
}

// UpdateProjectStatus 更新项目状态 0启用 1禁用
func (s *ProjectService) UpdateProjectStatus(req types.UpdateProjectStatusReq) *result.Result[bot.UpdateProjectStatusResp] {
	// 获取bot和账户信息
	b, accountInfo, err := s.svcCtx.GetBot(req.AccountId)
	if err != nil {
		return result.ToError[bot.UpdateProjectStatusResp](result.ErrorSelect.AddError(err))
	}

	resp, err := b.UpdateProjectStatus(req.AdvertiserId, req.StatusMap, accountInfo.Cookie)
	if err != nil {
		return result.ToError[bot.UpdateProjectStatusResp](result.ErrorSelect.AddError(err))
	}
	//添加操作日志
	go func() {
		if s.app != nil {
			descStr := ""
			for projectId, status := range req.StatusMap {
				descStr += fmt.Sprintf("项目[%s]状态更新为[%d], ", projectId, status)
			}
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   accountInfo.AccountName,
				AccountId:     accountInfo.AccountId,
				Module:        constant.OperationLog_Module_Project,
				OperationType: constant.OperationLog_OperationType_Update,
				Operation:     "更新项目状态",
				Description:   fmt.Sprintf("账号ID: %d，账户名: %s，[%d]广告主下更新项目状态: %s", accountInfo.AccountId, accountInfo.AccountName, req.AdvertiserId, descStr),
				OperationTime: time.Now(),
			})
		}
	}()
	return result.SuccessResult(*resp)
}

// UpdateProjectName 修改项目名称
func (s *ProjectService) UpdateProjectName(req types.UpdateProjectNameReq) *result.Result[bot.UpdateProjectNameResp] {
	// 获取bot和账户信息
	b, accountInfo, err := s.svcCtx.GetBot(req.AccountId)
	if err != nil {
		return result.ToError[bot.UpdateProjectNameResp](result.ErrorSelect.AddError(err))
	}

	resp, err := b.UpdateProjectName(req.AdvertiserId, req.ProjectId, req.Name, accountInfo.Cookie)
	if err != nil {
		return result.ToError[bot.UpdateProjectNameResp](result.ErrorSelect.AddError(err))
	}
	//添加操作日志
	go func() {
		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   accountInfo.AccountName,
				AccountId:     accountInfo.AccountId,
				Module:        constant.OperationLog_Module_Project,
				OperationType: constant.OperationLog_OperationType_Update,
				Operation:     "修改项目名称",
				Description:   fmt.Sprintf("账号ID: %d，账户名: %s，[%d]广告主下修改项目[%s]名称为[%s]", accountInfo.AccountId, accountInfo.AccountName, req.AdvertiserId, req.ProjectId, req.Name),
				OperationTime: time.Now(),
			})
		}
	}()
	return result.SuccessResult(*resp)
}

// RefreshProjects 刷新指定账户下的项目数据
func (s *ProjectService) RefreshProjects(accountId int64) *result.Result[string] {
	fmt.Printf("RefreshProjects 开始执行，accountId: %d\n", accountId)

	// 获取bot和账户信息
	b, accountInfo, err := s.svcCtx.GetBot(accountId)
	if err != nil {
		fmt.Printf("RefreshProjects 创建Bot实例失败: %v\n", err)
		return result.ToError[string](result.ErrorSelect.AddError(err))
	}
	fmt.Printf("RefreshProjects Bot实例创建成功\n")

	// 循环获取所有项目数据
	page := int64(1)
	pageSize := int64(100) // 每次获取100条数据
	hasMore := true
	allProjects := make([]model.Project, 0, 100)

	fmt.Printf("RefreshProjects 开始循环获取项目数据\n")
	for hasMore {
		fmt.Printf("RefreshProjects 正在获取第 %d 页数据\n", page)
		resp, err := b.GetProjectList(page, pageSize, 1, "", "", accountInfo.Cookie)
		if err != nil {
			fmt.Printf("RefreshProjects 获取第 %d 页数据失败: %v\n", page, err)
			return result.ToError[string](result.ErrorSelect.AddError(err))
		}
		fmt.Printf("RefreshProjects 第 %d 页获取成功，数据条数: %d\n", page, len(resp.Data.DataList))

		// 转换API响应数据为model.Project
		tempProjectList := make([]model.Project, 0, 100)
		if err := copier.CopyWithOption(&tempProjectList, resp.Data.DataList, copier.Option{Converters: []copier.TypeConverter{utils.FormatStringToTimeConverter(time.DateTime)}}); err != nil {
			fmt.Printf("RefreshProjects 数据转换失败: %v\n", err)
			return result.ToError[string](result.ErrorCopy.AddError(err))
		}
		allProjects = append(allProjects, tempProjectList...)

		// 检查是否还有更多数据
		hasMore = resp.Data.Pagination.HasMore
		fmt.Printf("RefreshProjects 第 %d 页处理完成，hasMore: %v，累计项目数: %d\n", page, hasMore, len(allProjects))
		page++
	}
	fmt.Printf("RefreshProjects 所有页面获取完成，总项目数: %d\n", len(allProjects))

	// 获取数据库中该账户下的项目ID列表（只查询必要字段）
	fmt.Printf("RefreshProjects 开始获取数据库中的项目列表\n")
	ProjectIdList, err := s.svcCtx.ProjectModel.GetProjectIdByAccountId(accountId)
	if err != nil {
		fmt.Printf("RefreshProjects 获取数据库项目列表失败: %v\n", err)
		return result.ToError[string](result.ErrorSelect.AddError(err))
	}
	fmt.Printf("RefreshProjects 数据库中现有项目数: %d\n", len(ProjectIdList))

	// 创建数据库中已存在的project_id映射
	projectIdMap := make(map[string]*model.Project)
	for _, proj := range ProjectIdList {
		projectIdMap[proj.ProjectId] = proj
	}

	// 创建API返回的project_id映射
	apiProjectMap := make(map[string]*model.Project)
	for i := range allProjects {
		apiProjectMap[allProjects[i].ProjectId] = &allProjects[i]
	}

	// 开始数据同步处理
	var toCreate []*model.Project // 需要新增的
	var toUpdate []*model.Project // 需要更新的
	var toDelete []string         // 需要标记删除的项目ID

	fmt.Printf("RefreshProjects 开始数据同步处理\n")

	// 处理API返回的数据（新增或更新）
	for projectId, apiProj := range apiProjectMap {
		apiProj.AccountId = accountId // 设置账户ID
		if dbProj, exists := projectIdMap[projectId]; exists {
			apiProj.Id = dbProj.Id                 // 保持数据库ID不变
			apiProj.CreateTime = dbProj.CreateTime // 保持创建时间不变
			apiProj.UpdateTime = time.Now()        // 更新时间为当前时间
			toUpdate = append(toUpdate, apiProj)
		} else { // 数据库中不存在，需要新增
			toCreate = append(toCreate, apiProj)
		}
	}

	// 处理需要标记删除的数据（数据库中存在但API中不存在）
	for projectId, dbProjId := range projectIdMap {
		if _, exists := apiProjectMap[projectId]; !exists {
			// API中不存在，需要标记删除
			if dbProjId.DeleteTime == 0 { // 检查是否已经标记删除（时间戳为0表示未删除）
				toDelete = append(toDelete, projectId)
			}
		}
	}

	fmt.Printf("RefreshProjects 数据统计 - 新增: %d, 更新: %d, 删除: %d\n", len(toCreate), len(toUpdate), len(toDelete))

	// 执行数据库操作
	if len(toCreate) > 0 {
		fmt.Printf("RefreshProjects 开始批量新增 %d 个项目\n", len(toCreate))
		if err := s.svcCtx.ProjectModel.BatchCreate(nil, toCreate); err != nil {
			fmt.Printf("RefreshProjects 批量新增失败: %v\n", err)
			return result.ToError[string](result.ErrorAdd.AddError(err))
		}
		fmt.Printf("RefreshProjects 批量新增成功\n")
	}

	// 更新操作
	if len(toUpdate) > 0 {
		fmt.Printf("RefreshProjects 开始更新 %d 个项目\n", len(toUpdate))
		for i, proj := range toUpdate {
			if err := s.svcCtx.ProjectModel.Update(nil, proj); err != nil {
				fmt.Printf("RefreshProjects 更新第 %d 个项目失败: %v\n", i+1, err)
				return result.ToError[string](result.ErrorUpdate.AddError(err))
			}
		}
		fmt.Printf("RefreshProjects 更新操作完成\n")
	}

	// 标记删除操作
	if len(toDelete) > 0 {
		fmt.Printf("RefreshProjects 开始标记删除 %d 个项目\n", len(toDelete))
		if err := s.svcCtx.ProjectModel.BatchMarkDelete(toDelete); err != nil {
			fmt.Printf("RefreshProjects 批量标记删除失败: %v\n", err)
			return result.ToError[string](result.ErrorDelete.AddError(err))
		}
		fmt.Printf("RefreshProjects 标记删除成功\n")
	}

	// 记录操作日志
	go func() {
		description := fmt.Sprintf("账号ID: %d，账户名: %s，刷新项目数据。新增: %d, 更新: %d, 删除: %d",
			accountInfo.AccountId, accountInfo.AccountName, len(toCreate), len(toUpdate), len(toDelete))

		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   accountInfo.AccountName,
				AccountId:     accountInfo.AccountId,
				Module:        constant.OperationLog_Module_Project,
				OperationType: constant.OperationLog_OperationType_Update,
				Operation:     "刷新项目数据",
				Description:   description,
				OperationTime: time.Now(),
			})
		}
	}()

	resultMsg := fmt.Sprintf("刷新项目数据成功，新增: %d, 更新: %d, 删除: %d", len(toCreate), len(toUpdate), len(toDelete))
	fmt.Printf("RefreshProjects 执行完成: %s\n", resultMsg)

	return result.SuccessResult[string](resultMsg)
}

// UpdateProjectStrategy 更新项目策略
func (s *ProjectService) UpdateProjectStrategy(req types.UpdateProjectStrategyReq) *result.Result[string] {
	// 验证请求参数
	if req.Id <= 0 {
		return result.ToError[string](result.ErrorReqParam.AddMessage("项目ID不能为空"))
	}

	// 更新项目策略
	updateData := map[string]interface{}{
		"strategies_id": req.StrategyIds,
	}

	if err := s.svcCtx.ProjectModel.MapUpdate(nil, req.Id, updateData); err != nil {
		return result.ToError[string](result.ErrorUpdate.AddError(err))
	}

	// 添加操作日志
	go func() {
		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   "",
				AccountId:     req.AccountId,
				Module:        constant.OperationLog_Module_Project,
				OperationType: constant.OperationLog_OperationType_Update,
				Operation:     "更新项目策略",
				Description:   fmt.Sprintf("项目ID: %s，策略ID: %s", req.ProjectId, req.StrategyIds),
				OperationTime: time.Now(),
			})
		}
	}()

	return result.SuccessResult("项目策略更新成功")
}

// CopyProject 复制项目
func (s *ProjectService) CopyProject(req types.CopyProjectReq) *result.Result[bot.CopyProjectResp] {
	// 验证复制数量
	if req.CopyCount <= 0 {
		req.CopyCount = 1
	}
	if req.CopyCount > 10 {
		return result.ToError[bot.CopyProjectResp](result.ErrorReqParam.AddMessage("复制数量不能超过10个"))
	}

	// 获取bot和账户信息
	b, accountInfo, err := s.svcCtx.GetBot(req.AccountId)
	if err != nil {
		return result.ToError[bot.CopyProjectResp](result.ErrorSelect.AddError(err))
	}

	// 如果是单个复制，使用原有逻辑
	if req.CopyCount == 1 {
		return s.copySingleProject(b, accountInfo, req)
	}

	// 批量复制
	return s.copyMultipleProjects(b, accountInfo, req)
}

// copySingleProject 复制单个项目
func (s *ProjectService) copySingleProject(b *bot.Bot, accountInfo *model.Account, req types.CopyProjectReq) *result.Result[bot.CopyProjectResp] {
	// 构建bot层的复制项目请求
	botReq := &bot.CopyProjectReq{
		SourceProjectID:     req.SourceProjectID,
		SourceAdvertiserID:  req.SourceAdvertiserID, // 源项目广告主ID
		AdvertiserID:        req.AdvertiserID,       // 目标广告主ID
		NewProjectName:      req.NewProjectName,
		Cookie:              accountInfo.Cookie,
		SourceProjectDetail: req.SourceProjectDetail, // 传递前端已修改的项目详情数据
	}

	resp, err := b.CopyProject(botReq)
	if err != nil {
		return result.ToError[bot.CopyProjectResp](result.ErrorSelect.AddError(err))
	}

	// 添加操作日志
	go func() {
		description := fmt.Sprintf("账号ID: %d，账户名: %s，[%d]广告主下复制项目[%s]",
			accountInfo.AccountId, accountInfo.AccountName, req.AdvertiserID, req.SourceProjectID)
		if req.NewProjectName != "" {
			description += fmt.Sprintf("，新项目名称: %s", req.NewProjectName)
		}
		// 添加是否使用前端数据的说明
		if req.SourceProjectDetail != nil {
			description += "，使用前端传递的项目详情数据"
		} else {
			description += "，后端重新获取项目详情数据"
		}

		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   accountInfo.AccountName,
				AccountId:     accountInfo.AccountId,
				Module:        constant.OperationLog_Module_Project,
				OperationType: constant.OperationLog_OperationType_Add,
				Operation:     "复制项目",
				Description:   description,
				OperationTime: time.Now(),
			})
		}
	}()

	return result.SuccessResult(*resp)
}

// copyMultipleProjects 批量复制项目
func (s *ProjectService) copyMultipleProjects(b *bot.Bot, accountInfo *model.Account, req types.CopyProjectReq) *result.Result[bot.CopyProjectResp] {
	// 生成基础项目名称
	baseProjectName := req.NewProjectName
	if baseProjectName == "" {
		// 如果没有指定名称，生成一个基础名称
		baseProjectName = fmt.Sprintf("项目_复制_%s", time.Now().Format("01_02_15:04:05"))
	}

	// 存储所有复制结果
	var allResults []*bot.CopyProjectResp
	var successResults []*bot.CopyProjectResp // 存储成功的结果
	var successCount int
	var failedCount int
	var lastError error

	// 批量复制项目
	for i := 1; i <= req.CopyCount; i++ {
		// 生成带序号的项目名称
		var newProjectName string
		if req.CopyCount == 1 {
			newProjectName = baseProjectName
		} else {
			newProjectName = fmt.Sprintf("%s_%02d", baseProjectName, i)
		}

		// 构建bot层的复制项目请求
		botReq := &bot.CopyProjectReq{
			SourceProjectID:     req.SourceProjectID,
			SourceAdvertiserID:  req.SourceAdvertiserID, // 源项目广告主ID
			AdvertiserID:        req.AdvertiserID,       // 目标广告主ID
			NewProjectName:      newProjectName,
			Cookie:              accountInfo.Cookie,
			SourceProjectDetail: req.SourceProjectDetail, // 传递前端获取的项目详情数据
		}

		resp, err := b.CopyProject(botReq)
		if err != nil {
			failedCount++
			lastError = err
			allResults = append(allResults, &bot.CopyProjectResp{
				Success:   false,
				Message:   fmt.Sprintf("复制第%d个项目失败: %v", i, err),
				ErrorCode: "COPY_PROJECT_FAILED",
			})
			continue
		}

		if resp.Success {
			successCount++
			allResults = append(allResults, resp)
		} else {
			failedCount++
			lastError = fmt.Errorf("复制第%d个项目失败: %s", i, resp.Message)
			allResults = append(allResults, resp)
		}

		// 在项目复制之间添加延迟，避免API调用过于频繁
		if i < req.CopyCount {
			time.Sleep(1 * time.Second) // 每个项目复制之间延迟1秒
		}
	}

	// 批量复制完成后，额外等待一段时间确保所有项目完全创建完成
	if req.CopyCount > 1 {
		time.Sleep(3 * time.Second) // 批量复制完成后额外等待3秒
	}

	// 构建批量复制响应
	var batchResp bot.CopyProjectResp
	if failedCount == 0 {
		// 全部成功
		batchResp = bot.CopyProjectResp{
			Success: true,
			Message: fmt.Sprintf("批量复制成功，共创建%d个项目", successCount),
		}
		// 使用最后一个成功的结果作为主要数据
		if len(allResults) > 0 {
			lastResult := allResults[len(allResults)-1]
			batchResp.SourceProject = lastResult.SourceProject
			batchResp.CreatedProject = lastResult.CreatedProject
		}
	} else if successCount == 0 {
		// 全部失败
		batchResp = bot.CopyProjectResp{
			Success:   false,
			Message:   fmt.Sprintf("批量复制失败，共%d个项目全部复制失败", req.CopyCount),
			ErrorCode: "BATCH_COPY_ALL_FAILED",
		}
	} else {
		// 部分成功
		batchResp = bot.CopyProjectResp{
			Success:   false,
			Message:   fmt.Sprintf("批量复制部分成功，成功%d个，失败%d个", successCount, failedCount),
			ErrorCode: "BATCH_COPY_PARTIAL_SUCCESS",
		}
		// 使用最后一个成功的结果作为主要数据
		for i := len(allResults) - 1; i >= 0; i-- {
			if allResults[i].Success {
				batchResp.SourceProject = allResults[i].SourceProject
				batchResp.CreatedProject = allResults[i].CreatedProject
				break
			}
		}
	}

	// 添加操作日志
	go func() {
		description := fmt.Sprintf("账号ID: %d，账户名: %s，[%d]广告主下批量复制项目[%s]，复制数量: %d，成功: %d，失败: %d",
			accountInfo.AccountId, accountInfo.AccountName, req.AdvertiserID, req.SourceProjectID, req.CopyCount, successCount, failedCount)
		if req.NewProjectName != "" {
			description += fmt.Sprintf("，基础项目名称: %s", req.NewProjectName)
		}

		// 添加是否使用前端数据的说明
		if req.SourceProjectDetail != nil {
			description += "，使用前端传递的项目详情数据"
		} else {
			description += "，后端重新获取项目详情数据"
		}

		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   accountInfo.AccountName,
				AccountId:     accountInfo.AccountId,
				Module:        constant.OperationLog_Module_Project,
				OperationType: constant.OperationLog_OperationType_Add,
				Operation:     "批量复制项目",
				Description:   description,
				OperationTime: time.Now(),
			})
		}
	}()

	if failedCount > 0 {
		return result.ToError[bot.CopyProjectResp](result.ErrorSelect.AddError(lastError))
	}

	return result.SuccessResult(batchResp)
}

// GetProjectDetail 获取项目详情
func (s *ProjectService) GetProjectDetail(req types.ProjectDetailReq) *result.Result[bot.GetProjectInfoResp] {
	// 获取bot和账户信息
	b, accountInfo, err := s.svcCtx.GetBot(req.AccountId)
	if err != nil {
		return result.ToError[bot.GetProjectInfoResp](result.ErrorSelect.AddError(err))
	}

	// 调用bot层获取项目详情
	resp, err := b.GetProjectInfo(req.AdvertiserID, []string{req.ProjectID}, accountInfo.Cookie)
	if err != nil {
		return result.ToError[bot.GetProjectInfoResp](result.ErrorSelect.AddError(err))
	}

	// 添加操作日志
	go func() {
		if s.app != nil {
			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   accountInfo.AccountName,
				AccountId:     accountInfo.AccountId,
				Module:        constant.OperationLog_Module_Project,
				OperationType: constant.OperationLog_OperationType_Select,
				Operation:     "获取项目详情",
				Description:   fmt.Sprintf("账号ID: %d，账户名: %s，获取项目[%s]详情", accountInfo.AccountId, accountInfo.AccountName, req.ProjectID),
				OperationTime: time.Now(),
			})
		}
	}()

	return result.SuccessResult(*resp)
}

// BatchUpdateProjectBid 批量修改项目出价
func (s *ProjectService) BatchUpdateProjectBid(req types.BatchUpdateProjectBidReq) *result.Result[bot.BatchUpdateProjectBidResp] {
	// 获取bot和账户信息
	b, accountInfo, err := s.svcCtx.GetBot(req.AccountId)
	if err != nil {
		return result.ToError[bot.BatchUpdateProjectBidResp](result.ErrorNewBot.AddError(err))
	}

	resp, err := b.BatchUpdateProjectBid(req.AdvertiserId, req.PromotionBidMap, req.IsAsync, accountInfo.Cookie)
	if err != nil {
		return result.ToError[bot.BatchUpdateProjectBidResp](result.ErrorUpdate.AddError(err))
	}

	// 添加操作日志
	go func() {
		if s.app != nil {
			bidDescStr := ""
			for projectId, bid := range req.PromotionBidMap {
				bidDescStr += fmt.Sprintf("项目[%s]出价更新为[%s], ", projectId, bid)
			}

			asyncStr := "同步"
			if req.IsAsync {
				asyncStr = "异步"
			}

			s.app.EmitEvent(constant.Runtime_Event_OperationLog, model.OperationLog{
				AccountName:   accountInfo.AccountName,
				AccountId:     accountInfo.AccountId,
				Module:        constant.OperationLog_Module_Project,
				OperationType: constant.OperationLog_OperationType_Update,
				Operation:     "批量修改项目出价",
				Description:   fmt.Sprintf("账号ID: %d，账户名: %s，[%d]广告主下%s批量修改项目出价: %s", accountInfo.AccountId, accountInfo.AccountName, req.AdvertiserId, asyncStr, bidDescStr),
				OperationTime: time.Now(),
			})
		}
	}()

	return result.SuccessResult(*resp)
}
