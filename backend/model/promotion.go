package model

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"gitlab.e-idear.com/enginer/app/oceanEngineManager/backend/constant"
	"gorm.io/gorm"
)

// Promotion 广告推广模型
type (
	Promotion struct {
		Id        int64 `gorm:"column:id;primaryKey;autoIncrement;comment:数据库主键ID" json:"id"`               // 数据库主键ID
		AccountId int64 `gorm:"column:account_id;uniqueIndex:idx_promotion;comment:账户ID" json:"account_id"` // 账户ID

		PromotionId    string  `gorm:"column:promotion_id;uniqueIndex:idx_promotion;comment:广告推广ID" json:"promotion_id"` // 广告推广ID
		PromotionName  string  `gorm:"column:promotion_name;comment:广告推广名称" json:"promotion_name"`                       // 广告推广名称
		Ctr            float64 `gorm:"column:ctr;comment:广告点击率" json:"ctr"`                                              // 广告点击率
		ConversionRate float64 `gorm:"column:conversion_rate;comment:广告转化率" json:"conversion_rate"`                      // 广告转化率
		AdvertiserId   int64   `gorm:"column:advertiser_id;comment:广告主ID" json:"advertiser_id"`                          // 广告主ID
		ProjectId      *string `gorm:"column:project_id;comment:项目ID" json:"project_id,omitempty"`                       // 项目ID

		PromotionStatus              int                       `gorm:"column:promotion_status;comment:广告推广状态" json:"promotion_status"`                                                      // 广告推广状态
		PromotionStatusName          string                    `gorm:"column:promotion_status_name;comment:广告推广状态名称" json:"promotion_status_name"`                                          // 广告推广状态名称
		AdBudget                     string                    `gorm:"column:ad_budget;comment:广告预算" json:"ad_budget"`                                                                      // 广告预算
		DeliveryMode                 int                       `gorm:"column:delivery_mode;comment:广告投放模式" json:"delivery_mode"`                                                            // 广告投放模式
		LandingType                  int                       `gorm:"column:landing_type;comment:广告落地页类型" json:"landing_type"`                                                             // 广告落地页类型
		LandingTypeName              string                    `gorm:"column:landing_type_name;comment:广告落地页类型名称" json:"landing_type_name"`                                                 // 广告落地页类型名称
		AdPricing                    int                       `gorm:"column:ad_pricing;comment:广告定价方式" json:"ad_pricing"`                                                                  // 广告定价方式
		AdPricingName                string                    `gorm:"column:ad_pricing_name;comment:广告定价方式名称" json:"ad_pricing_name"`                                                      // 广告定价方式名称
		AdSmartBidType               int                       `gorm:"column:ad_smart_bid_type;comment:广告智能出价类型" json:"ad_smart_bid_type"`                                                  // 广告智能出价类型
		AdId                         int64                     `gorm:"column:ad_id;comment:广告ID" json:"ad_id"`                                                                              // 广告ID
		AdBid                        string                    `gorm:"column:ad_bid;comment:广告出价" json:"ad_bid"`                                                                            // 广告出价
		AdOptStatus                  int                       `gorm:"column:ad_opt_status;comment:广告优化状态" json:"ad_opt_status"`                                                            // 广告优化状态
		ProjectName                  string                    `gorm:"column:project_name;comment:项目名称" json:"project_name"`                                                                // 项目名称
		ProjectStatus                int                       `gorm:"column:project_status;comment:项目状态" json:"project_status"`                                                            // 项目状态
		ProjectStatusName            string                    `gorm:"column:project_status_name;comment:项目状态名称" json:"project_status_name"`                                                // 项目状态名称
		StartTime                    string                    `gorm:"column:start_time;comment:广告开始时间" json:"start_time"`                                                                  // 广告开始时间
		EndTime                      string                    `gorm:"column:end_time;comment:广告结束时间" json:"end_time"`                                                                      // 广告结束时间
		DownloadTypeName             string                    `gorm:"column:download_type_name;comment:广告下载类型名称" json:"download_type_name"`                                                // 广告下载类型名称
		ExternalAction               int                       `gorm:"column:external_action;comment:广告外部行为" json:"external_action"`                                                        // 广告外部行为
		ExternalActionName           string                    `gorm:"column:external_action_name;comment:广告外部行为名称" json:"external_action_name"`                                            // 广告外部行为名称
		ActionTrackUrl               string                    `gorm:"column:action_track_url;comment:广告行为跟踪URL" json:"action_track_url"`                                                   // 广告行为跟踪URL
		AppCode                      []int                     `gorm:"column:app_code;type:json;serializer:json;comment:广告应用代码" json:"app_code"`                                            // 广告应用代码
		AppCodeName                  []string                  `gorm:"column:app_code_name;type:json;serializer:json;comment:广告应用代码名称" json:"app_code_name"`                                // 广告应用代码名称
		CampaignBudget               string                    `gorm:"column:campaign_budget;comment:广告推广活动预算" json:"campaign_budget"`                                                      // 广告推广活动预算
		DeepCpaBid                   string                    `gorm:"column:deep_cpa_bid;comment:广告深度CPA出价" json:"deep_cpa_bid"`                                                           // 广告深度CPA出价
		ModifyTime                   string                    `gorm:"column:modify_time;comment:广告修改时间" json:"modify_time"`                                                                // 广告修改时间
		DeliverySceneName            string                    `gorm:"column:delivery_scene_name;comment:广告投放场景名称" json:"delivery_scene_name"`                                              // 广告投放场景名称
		DeepBidType                  int                       `gorm:"column:deep_bid_type;comment:广告深度出价类型" json:"deep_bid_type"`                                                          // 广告深度出价类型
		CompensateInvalidReasons     string                    `gorm:"column:compensate_invalid_reasons;comment:广告补偿无效原因" json:"compensate_invalid_reasons"`                                // 广告补偿无效原因
		CompensateEndReasons         string                    `gorm:"column:compensate_end_reasons;comment:广告补偿结束原因" json:"compensate_end_reasons"`                                        // 广告补偿结束原因
		CompensateAmount             string                    `gorm:"column:compensate_amount;comment:广告补偿金额" json:"compensate_amount"`                                                    // 广告补偿金额
		CompensateUrl                string                    `gorm:"column:compensate_url;comment:广告补偿URL" json:"compensate_url"`                                                         // 广告补偿URL
		CompensateStatus             int                       `gorm:"column:compensate_status;comment:广告补偿状态" json:"compensate_status"`                                                    // 广告补偿状态
		PromotionAggregateModifyTime string                    `gorm:"column:promotion_aggregate_modify_time;comment:推广聚合修改时间" json:"promotion_aggregate_modify_time"`                      // 推广聚合修改时间
		PromotionStatusFirst         int                       `gorm:"column:promotion_status_first;comment:推广状态第一级" json:"promotion_status_first"`                                         // 推广状态第一级
		PromotionStatusSecond        []int                     `gorm:"column:promotion_status_second;type:json;serializer:json;comment:推广状态第二级" json:"promotion_status_second"`             // 推广状态第二级
		PromotionStatusFirstName     string                    `gorm:"column:promotion_status_first_name;comment:推广状态第一级名称" json:"promotion_status_first_name"`                             // 推广状态第一级名称
		PromotionStatusSecondName    []string                  `gorm:"column:promotion_status_second_name;type:json;serializer:json;comment:推广状态第二级名称" json:"promotion_status_second_name"` // 推广状态第二级名称
		ProjectStatusFirst           int                       `gorm:"column:project_status_first;comment:项目状态第一级" json:"project_status_first"`                                             // 项目状态第一级
		ProjectStatusSecond          []int                     `gorm:"column:project_status_second;type:json;serializer:json;comment:项目状态第二级" json:"project_status_second"`                 // 项目状态第二级
		ProjectStatusFirstName       string                    `gorm:"column:project_status_first_name;comment:项目状态第一级名称" json:"project_status_first_name"`                                 // 项目状态第一级名称
		ProjectStatusSecondName      []string                  `gorm:"column:project_status_second_name;type:json;serializer:json;comment:项目状态第二级名称" json:"project_status_second_name"`     // 项目状态第二级名称
		DeepExternalActionName       string                    `gorm:"column:deep_external_action_name;comment:广告深度外部行为名称" json:"deep_external_action_name"`                                // 广告深度外部行为名称
		DeliveryPackage              int                       `gorm:"column:delivery_package;comment:广告投放包" json:"delivery_package"`                                                       // 广告投放包
		ProjectBid                   string                    `gorm:"column:project_bid;comment:项目出价" json:"project_bid"`                                                                  // 项目出价
		DeliveryModeInternal         int                       `gorm:"column:delivery_mode_internal;comment:广告内部投放模式" json:"delivery_mode_internal"`                                        // 广告内部投放模式
		MarketingInfo                GetPromotionMarketingInfo `gorm:"column:marketing_info;type:json;serializer:json;comment:广告营销信息" json:"marketing_info"`                                // 广告营销信息
		AdvertiserName               string                    `gorm:"column:advertiser_name;comment:广告主名称" json:"advertiser_name"`                                                         // 广告主名称
		ShopMultiRoiGoals            GetPromotionRoiGoals      `gorm:"column:shop_multi_roi_goals;type:json;serializer:json;comment:广告店铺多ROI目标" json:"shop_multi_roi_goals"`                // 广告店铺多ROI目标
		PromotionCreateTime          string                    `gorm:"column:promotion_create_time;comment:推广创建时间" json:"promotion_create_time"`                                            // 推广创建时间
		PromotionModifyTime          string                    `gorm:"column:promotion_modify_time;comment:推广修改时间" json:"promotion_modify_time"`                                            // 推广修改时间
		CampaignId                   int64                     `gorm:"column:campaign_id;comment:推广活动ID" json:"campaign_id"`                                                                // 推广活动ID
		DeliveryProduct              int                       `gorm:"column:delivery_product;comment:广告投放产品" json:"delivery_product"`                                                      // 广告投放产品
		DeliveryMedium               int                       `gorm:"column:delivery_medium;comment:广告投放媒介" json:"delivery_medium"`                                                        // 广告投放媒介
		ClickCnt                     string                    `gorm:"column:click_cnt;comment:广告点击次数" json:"click_cnt"`                                                                    // 广告点击次数
		CpcPlatform                  string                    `gorm:"column:cpc_platform;comment:广告平均点击成本" json:"cpc_platform"`                                                            // 广告平均点击成本
		ConversionCost               string                    `gorm:"column:conversion_cost;comment:广告转化成本" json:"conversion_cost"`                                                        // 广告转化成本
		ShowCnt                      string                    `gorm:"column:show_cnt;comment:广告展示次数" json:"show_cnt"`                                                                      // 广告展示次数
		CpmPlatform                  string                    `gorm:"column:cpm_platform;comment:广告千次展示成本" json:"cpm_platform"`                                                            // 广告千次展示成本
		StatCost                     string                    `gorm:"column:stat_cost;comment:广告统计消耗" json:"stat_cost"`                                                                    // 广告统计消耗
		AdBudgetMode                 int                       `gorm:"column:ad_budget_mode;comment:广告预算模式" json:"ad_budget_mode,omitempty"`                                                // 广告预算模式
		AdBudgetModeName             string                    `gorm:"column:ad_budget_mode_name;comment:广告预算模式名称" json:"ad_budget_mode_name,omitempty"`                                    // 广告预算模式名称
		NativeType                   int                       `gorm:"column:native_type;comment:广告原生类型" json:"native_type,omitempty"`                                                      // 广告原生类型
		IesCoreUserId                int64                     `gorm:"column:ies_core_user_id;comment:IES核心用户ID" json:"ies_core_user_id,omitempty"`                                         // IES核心用户ID
		AnchorRelatedType            int                       `gorm:"column:anchor_related_type;comment:广告主播关联类型" json:"anchor_related_type,omitempty"`                                    // 广告主播关联类型
		IsFeedAndFavSee              int                       `gorm:"column:is_feed_and_fav_see;comment:广告是否推荐和收藏可见" json:"is_feed_and_fav_see,omitempty"`                                 // 广告是否推荐和收藏可见
		MaterialScore                GetPromotionMaterialScore `gorm:"column:material_score;type:json;serializer:json;comment:广告素材评分" json:"material_score,omitempty"`                      // 广告素材评分
		BudgetOptimizeSwitch         int                       `gorm:"column:budget_optimize_switch;comment:广告预算优化开关" json:"budget_optimize_switch,omitempty"`                              // 广告预算优化开关
		NativeInfoReason             string                    `gorm:"column:native_info_reason;comment:广告原生信息原因" json:"native_info_reason,omitempty"`                                      // 广告原生信息原因
		ConvertCnt                   string                    `gorm:"column:convert_cnt;comment:广告转化次数" json:"convert_cnt"`                                                                // 广告转化次数
		PreConvertCost               string                    `gorm:"column:pre_convert_cost;comment:预转化成本" json:"pre_convert_cost"`                                                       // 预转化成本
		PreConvertCount              string                    `gorm:"column:pre_convert_count;comment:预转化数" json:"pre_convert_count"`                                                      // 预转化数
		SearchFirstPositionStatCost  string                    `gorm:"column:search_first_position_stat_cost;comment:搜索首位消耗" json:"search_first_position_stat_cost"`                        // 搜索首位消耗
		LearningStatus               int                       `gorm:"column:learning_status;comment:学习状态" json:"learning_status"`                                                          // 学习状态
		LearningConvertCnt           string                    `gorm:"column:learning_convert_cnt;comment:学习转化数" json:"learning_convert_cnt"`                                               // 学习转化数
		CreateTime                   time.Time                 `gorm:"column:create_time;comment:创建时间" json:"create_time"`                                                                  // 创建时间
		UpdateTime                   time.Time                 `gorm:"column:update_time;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"`                                        // 更新时间
		DeleteTime                   int64                     `gorm:"column:delete_time;comment:删除时间" json:"delete_time"`                                                                  // 删除时间（Unix时间戳）
		StrategyIds                  []int64                   `gorm:"-" json:"strategy_ids"`                                                                                               // 策略ID列表（非数据库字段）
	}

	// GetPromotionMarketingInfo 推广营销信息
	GetPromotionMarketingInfo struct {
		MarketingGoal      int `json:"marketingGoal"`                // 营销目标
		DeliveryRelatedNum int `json:"deliveryRelatedNum,omitempty"` // 投放相关数量
		AutoAdType         int `json:"autoAdType,omitempty"`         // 自动广告类型
	}

	// GetPromotionRoiGoals 推广ROI目标
	GetPromotionRoiGoals struct {
		// 预留字段，根据实际需求添加
	}

	// GetPromotionMaterialScore 推广素材评分
	GetPromotionMaterialScore struct {
		TaskId          string   `json:"taskId"`                                                                              // 任务ID
		NumOfMaterial   int      `json:"numOfMaterial"`                                                                       // 素材数量
		TypeOfMaterial  int      `json:"typeOfMaterial"`                                                                      // 素材类型
		ValueOfMaterial int      `json:"valueOfMaterial"`                                                                     // 素材价值
		MaterialAdvice  []string `gorm:"column:material_advice;type:json;serializer:json;comment:素材建议" json:"materialAdvice"` // 素材建议
	}
)

func (m *GetPromotionMarketingInfo) Value() (driver.Value, error) {
	return json.Marshal(m)
}

func (m *GetPromotionMarketingInfo) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("failed to unmarshal GetPromotionMarketingInfo")
	}
	return json.Unmarshal(bytes, m)
}
func (m *GetPromotionRoiGoals) Value() (driver.Value, error) {
	return json.Marshal(m)
}

func (m *GetPromotionRoiGoals) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("failed to unmarshal GetPromotionRoiGoals")
	}
	return json.Unmarshal(bytes, m)
}
func (m *GetPromotionMaterialScore) Value() (driver.Value, error) {
	return json.Marshal(m)
}

func (m *GetPromotionMaterialScore) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("failed to unmarshal GetPromotionMaterialScore")
	}
	return json.Unmarshal(bytes, m)
}

// TableName 指定表名
func (Promotion) TableName() string {
	return "promotion"
}

// PromotionModel 广告推广模型
type PromotionModel struct {
	db *gorm.DB
}

// NewPromotionModel 创建广告推广模型
func NewPromotionModel(db *gorm.DB) *PromotionModel {
	return &PromotionModel{
		db: db,
	}
}

// Create 创建广告推广
func (m *PromotionModel) Create(promotion *Promotion) error {
	return m.db.Create(promotion).Error
}

// Update 更新广告推广
func (m *PromotionModel) Update(tx *gorm.DB, promotion *Promotion) error {
	db := m.db
	if tx != nil {
		db = tx
	}
	return db.Select("*").Updates(promotion).Error
}

// MapUpdate 使用Map更新广告推广
func (m *PromotionModel) MapUpdate(tx *gorm.DB, id int64, data map[string]interface{}) error {
	db := m.db
	if tx != nil {
		db = tx
	}
	return db.Model(&Promotion{}).Where("id = ?", id).Updates(data).Error
}

// Save 保存广告推广
func (m *PromotionModel) Save(tx *gorm.DB, promotion *Promotion) error {
	db := m.db
	if tx != nil {
		db = tx
	}
	return db.Save(promotion).Error
}

// Delete 删除广告推广
func (m *PromotionModel) Delete(promotion *Promotion) error {
	return m.db.Delete(promotion).Error
}

// BatchDelete 批量删除广告推广
func (m *PromotionModel) BatchDelete(ids []int64) error {
	return m.db.Where("id IN ?", ids).Delete(&Promotion{}).Error
}

// GetById 根据ID获取广告推广
func (m *PromotionModel) GetById(id int64) (*Promotion, error) {
	var promotion Promotion
	if err := m.db.First(&promotion, id).Error; err != nil {
		return nil, err
	}
	return &promotion, nil
}

// GetByPromotionId 根据PromotionId获取广告推广
func (m *PromotionModel) GetByPromotionId(promotionId string) (*Promotion, error) {
	var promotion Promotion
	if err := m.db.Where("promotion_id = ?", promotionId).First(&promotion).Error; err != nil {
		return nil, err
	}
	return &promotion, nil
}

// GetByAdvertiserId 根据AdvertiserId获取广告推广列表
func (m *PromotionModel) GetByAdvertiserId(advertiserId int64) ([]*Promotion, error) {
	var promotions []*Promotion
	if err := m.db.Where("advertiser_id = ?", advertiserId).Find(&promotions).Error; err != nil {
		return nil, err
	}
	return promotions, nil
}

// GetByAccountId 根据AccountId获取广告推广列表
func (m *PromotionModel) GetByAccountId(accountId int64) ([]*Promotion, error) {
	var promotions []*Promotion
	if err := m.db.Where("account_id = ?", accountId).Find(&promotions).Error; err != nil {
		return nil, err
	}
	return promotions, nil
}

// GetByProjectId 根据ProjectId获取广告推广列表
func (m *PromotionModel) GetByProjectId(projectId string) ([]*Promotion, error) {
	var promotions []*Promotion
	if err := m.db.Where("project_id = ? AND project_id IS NOT NULL", projectId).Find(&promotions).Error; err != nil {
		return nil, err
	}
	return promotions, nil
}

// GetEnabledPromotionsByProjectId 根据ProjectId获取启用状态的广告推广列表
func (m *PromotionModel) GetEnabledPromotionsByProjectId(projectId string) ([]*Promotion, error) {
	var promotions []*Promotion
	// 新的状态结构：查询父级状态为"投放中"的广告
	if err := m.db.Where("project_id = ? AND project_id IS NOT NULL AND promotion_status_first = ? AND promotion_status_first_name = ?", projectId, constant.PromotionStatusFirst_Active, constant.PromotionStatusFirstName_Active).Find(&promotions).Error; err != nil {
		return nil, err
	}
	return promotions, nil
}

// 根据项目id获取广告推广列表
func (m *PromotionModel) GetPromotionsByProjectId(projectId string) ([]*Promotion, error) {
	var promotions []*Promotion
	if err := m.db.Where("project_id = ? AND project_id IS NOT NULL", projectId).Find(&promotions).Error; err != nil {
		return nil, err
	}
	return promotions, nil
}

// List 获取广告推广列表
func (m *PromotionModel) List(page, pageSize int, conditions map[string]interface{}) ([]*Promotion, int64, error) {
	var promotions []*Promotion
	var total int64

	db := m.db.Model(&Promotion{})

	// 添加查询条件
	for key, value := range conditions {
		if strValue, ok := value.(string); ok && strValue != "" {
			if key == "promotion_name" || key == "remark" {
				db = db.Where(key+" LIKE ?", "%"+strValue+"%")
			} else {
				db = db.Where(key+" = ?", value)
			}
		} else if value != nil {
			db = db.Where(key+" = ?", value)
		}
	}

	// 分页查询
	if page > 0 && pageSize > 0 {
		// 获取总数
		if err := db.Count(&total).Error; err != nil {
			return nil, 0, err
		}
		db = db.Offset((page - 1) * pageSize).Limit(pageSize)
	}

	// 分页查询
	if err := db.Find(&promotions).Error; err != nil {
		return nil, 0, err
	}
	if page > 0 && pageSize > 0 {
		total = int64(len(promotions))
	}
	return promotions, total, nil
}

// GetStat 获取广告推广统计信息
func (m *PromotionModel) GetStat() (map[string]interface{}, error) {
	var totalCount int64
	var activeCount int64

	if err := m.db.Model(&Promotion{}).Count(&totalCount).Error; err != nil {
		return nil, err
	}

	if err := m.db.Model(&Promotion{}).Where("status = ?", "active").Count(&activeCount).Error; err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"total_count":  totalCount,
		"active_count": activeCount,
	}, nil
}

// BatchCreate 批量创建广告推广
func (m *PromotionModel) BatchCreate(tx *gorm.DB, promotions []*Promotion) error {
	db := m.db
	if tx != nil {
		db = tx
	}
	return db.CreateInBatches(promotions, 400).Error
}

// BatchMarkDelete 批量标记删除广告推广
func (m *PromotionModel) BatchMarkDelete(promotionIds []string) error {
	return m.db.Model(&Promotion{}).Where("promotion_id IN ?", promotionIds).Update("delete_time", time.Now().Unix()).Error
}

// GetPromotionIdByAccountId 根据账户ID获取广告推广ID列表
func (m *PromotionModel) GetPromotionIdByAccountId(accountId int64) ([]*Promotion, error) {
	var promotions []*Promotion
	if err := m.db.Select("id, promotion_id, create_time, delete_time").Where("account_id = ?", accountId).Find(&promotions).Error; err != nil {
		return nil, err
	}
	return promotions, nil
}
