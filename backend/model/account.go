package model

import (
	"time"

	"gorm.io/gorm"
)

// Account 账户模型
type Account struct {
	Id               int64     `gorm:"column:id;primaryKey;autoIncrement;comment:数据库主键ID" json:"id"`                 // 数据库主键ID
	AccountName      string    `gorm:"column:account_name;comment:账户名称" json:"account_name"`                         // 账户名称
	AccountId        int64     `gorm:"column:account_id;uniqueIndex:idx_account;comment:账户ID" json:"account_id"`     // 账户ID（巨量引擎平台ID）
	LoginType        string    `gorm:"column:login_type;comment:登录类型" json:"login_type"`                             // 登录类型（Cookie/Email/SMS）
	Cookie           string    `gorm:"column:cookie;comment:Cookie信息" json:"cookie"`                                 // Cookie信息
	Email            string    `gorm:"column:email;comment:邮箱" json:"email"`                                         // 邮箱
	Password         string    `gorm:"column:password;comment:密码" json:"password"`                                   // 密码
	Mobile           string    `gorm:"column:mobile;comment:手机号" json:"mobile"`                                      // 手机号
	ProxyId          int64     `gorm:"column:proxy_id;comment:代理ID" json:"proxy_id"`                                 // 代理ID
	CreateTime       time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"` // 创建时间
	UpdateTime       time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"` // 更新时间
	Remark           *string   `gorm:"column:remark;comment:备注" json:"remark"`                                       // 备注
	SubAccountCount  int       `gorm:"column:sub_account_count;comment:子账户数量" json:"sub_account_count"`              // 子账户数量
	DailyConsumption string    `gorm:"column:daily_consumption;comment:日消耗" json:"daily_consumption"`                // 日消耗
	DailyConversion  string    `gorm:"column:daily_conversion;comment:日转化数" json:"daily_conversion"`                 // 日转化数
	DailyClicks      string    `gorm:"column:daily_clicks;comment:日点击数" json:"daily_clicks"`                         // 日点击数
	DailyViews       string    `gorm:"column:daily_views;comment:日浏览量" json:"daily_views"`                           // 日浏览量
}

// TableName 指定表名
func (Account) TableName() string {
	return "account"
}

// AccountModel 账号表模型
type AccountModel struct {
	db *gorm.DB
}

// NewAccountModel 创建账号模型
func NewAccountModel(db *gorm.DB) *AccountModel {
	return &AccountModel{
		db: db,
	}
}

// Create 创建新账号
func (m *AccountModel) Create(account *Account) error {
	return m.db.Create(account).Error
}

// Update 更新账号信息
func (m *AccountModel) Update(tx *gorm.DB, account *Account) error {
	db := m.db
	if tx != nil {
		db = tx
	}
	return db.Updates(account).Error
}

// MapUpdate 更新账号信息
func (m *AccountModel) MapUpdate(tx *gorm.DB, id int64, account map[string]interface{}) error {
	db := m.db
	if tx != nil {
		db = tx
	}
	return db.Model(&Account{}).Where("id = ?", id).Updates(account).Error
}

// Save 保存账号信息
func (m *AccountModel) Save(tx *gorm.DB, account *Account) error {
	db := m.db
	if tx != nil {
		db = tx
	}
	return db.Save(account).Error
}

// Delete 删除账号
func (m *AccountModel) Delete(account *Account) error {
	return m.db.Delete(account).Error
}

// GetById 根据ID获取账号
func (m *AccountModel) GetById(id int64) (*Account, error) {
	var account Account
	if err := m.db.First(&account, id).Error; err != nil {
		return nil, err
	}
	return &account, nil
}

// GetByAccountId 根据AccountId获取账号
func (m *AccountModel) GetByAccountId(accountId int64) (*Account, error) {
	var account Account
	if err := m.db.Where("account_id = ?", accountId).First(&account).Error; err != nil {
		return nil, err
	}
	return &account, nil
}

// List 获取账号列表
func (m *AccountModel) List(page, pageSize int, conditions map[string]interface{}) ([]*Account, int64, error) {
	var accounts []*Account
	var total int64

	db := m.db.Model(&Account{})

	// 添加查询条件
	for key, value := range conditions {
		if strValue, ok := value.(string); ok && strValue != "" {
			if key == "account_name" || key == "remark" {
				db = db.Where(key+" LIKE ?", "%"+strValue+"%")
			} else {
				db = db.Where(key+" = ?", value)
			}
		} else if value != nil {
			db = db.Where(key+" = ?", value)
		}
	}

	// 获取总数
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	if err := db.Offset((page - 1) * pageSize).Limit(pageSize).Find(&accounts).Error; err != nil {
		return nil, 0, err
	}

	return accounts, total, nil
}

// ListAll 获取所有账号
func (m *AccountModel) ListAll() ([]*Account, error) {
	var accounts []*Account
	if err := m.db.Find(&accounts).Error; err != nil {
		return nil, err
	}
	return accounts, nil
}

// GetStat 获取账号统计信息
func (m *AccountModel) GetStat() (map[string]interface{}, error) {
	var totalCount int64
	var activeCount int64

	if err := m.db.Model(&Account{}).Count(&totalCount).Error; err != nil {
		return nil, err
	}

	if err := m.db.Model(&Account{}).Where("account_id IS NOT NULL AND account_id != ''").Count(&activeCount).Error; err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"total_count":  totalCount,
		"active_count": activeCount,
	}, nil
}

// BatchDelete 批量删除账号
func (m *AccountModel) BatchDelete(ids []int64) error {
	return m.db.Where("id IN ?", ids).Delete(&Account{}).Error
}
